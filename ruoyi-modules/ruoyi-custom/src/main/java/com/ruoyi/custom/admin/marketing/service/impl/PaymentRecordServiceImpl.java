package com.ruoyi.custom.admin.marketing.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.uuid.IdUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.custom.admin.balance.domain.BalanceInfo;
import com.ruoyi.custom.admin.balance.domain.SecurityBalanceInfo;
import com.ruoyi.custom.admin.balance.service.IBalanceInfoService;
import com.ruoyi.custom.admin.balance.service.ISecurityBalanceInfoService;
import com.ruoyi.custom.admin.elderlyPeople.domain.ElderlyPeopleInfo;
import com.ruoyi.custom.admin.elderlyPeople.service.IElderlyPeopleInfoService;
import com.ruoyi.custom.admin.fee.domain.req.FeePaySaveVo;
import com.ruoyi.custom.admin.fee.service.IFeePayInfoService;
import com.ruoyi.custom.admin.liveManage.domain.LiveBaseInfo;
import com.ruoyi.custom.admin.liveManage.domain.LiveBedRecords;
import com.ruoyi.custom.admin.liveManage.domain.LiveComboRecords;
import com.ruoyi.custom.admin.liveManage.domain.req.LiveQueryVo;
import com.ruoyi.custom.admin.liveManage.domain.res.LiveInfoRes;
import com.ruoyi.custom.admin.liveManage.mapper.LiveBaseInfoMapper;
import com.ruoyi.custom.admin.liveManage.service.ILiveBaseInfoService;
import com.ruoyi.custom.admin.liveManage.service.ILiveBedRecordsService;
import com.ruoyi.custom.admin.liveManage.service.ILiveComboRecordsService;
import com.ruoyi.custom.admin.marketing.constant.FeeType;
import com.ruoyi.custom.admin.marketing.domain.PaymentChangeRecord;
import com.ruoyi.custom.admin.marketing.domain.PaymentRecord;
import com.ruoyi.custom.admin.marketing.dto.PaymentRecordDTO;
import com.ruoyi.custom.admin.marketing.mapper.ContractInfoMapper;
import com.ruoyi.custom.admin.marketing.mapper.PaymentChangeRecordMapper;
import com.ruoyi.custom.admin.marketing.mapper.PaymentRecordMapper;
import com.ruoyi.custom.admin.marketing.req.FeeStatisticsReq;
import com.ruoyi.custom.admin.marketing.resp.FeeStatisticsResp;
import com.ruoyi.custom.admin.marketing.resp.PaymentDetailResp;
import com.ruoyi.custom.admin.marketing.resp.PaymentManagementResp;
import com.ruoyi.custom.admin.marketing.resp.PaymentRemindResp;
import com.ruoyi.custom.admin.marketing.service.IPaymentRecordService;
import com.ruoyi.custom.admin.service.domain.UserValueAddedServiceBill;
import com.ruoyi.custom.admin.service.service.IUserValueAddedServiceService;
import com.ruoyi.custom.utils.OrderUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.ruoyi.custom.admin.marketing.constant.FeeType.ADDED_SERVICE;

/**
 * 缴费确认单Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-18
 */
@Service
@Slf4j
public class PaymentRecordServiceImpl implements IPaymentRecordService {
    @Autowired
    private PaymentRecordMapper paymentRecordMapper;

    @Autowired
    private ContractInfoMapper contractInfoMapper;

    @Autowired
    private IBalanceInfoService balanceInfoService;

    @Autowired
    private IFeePayInfoService feePayInfoService;

    @Autowired
    private IUserValueAddedServiceService userValueAddedServiceService;

    @Autowired
    private ILiveBaseInfoService liveBaseInfoService;

    @Autowired
    private ILiveBedRecordsService liveBedRecordsService;

    @Autowired
    private ILiveComboRecordsService liveComboRecordsService;

    @Autowired
    private LiveBaseInfoMapper liveBaseInfoMapper;

    @Autowired
    private IElderlyPeopleInfoService elderlyPeopleInfoService;

    @Autowired
    private PaymentChangeRecordMapper paymentChangeRecordMapper;

    @Autowired
    private ISecurityBalanceInfoService securityBalanceInfoService;


    /**
     * 查询缴费确认单
     *
     * @param id 缴费确认单主键
     * @return 缴费确认单
     */
    @Override
    public PaymentRecord selectPaymentRecordById(String id) {
        return paymentRecordMapper.selectPaymentRecordById(id);
    }

    /**
     * 查询缴费确认单列表
     *
     * @param paymentRecord 缴费确认单
     * @return 缴费确认单
     */
    @Override
    public List<PaymentRecord> selectPaymentRecordList(PaymentRecord paymentRecord) {
        return paymentRecordMapper.selectPaymentRecordList(paymentRecord);
    }

    /**
     * 缴费（直接确认）
     *
     * @param paymentRecord 缴费确认单
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int payment(PaymentRecord paymentRecord) {
        if (StrUtil.isBlank(paymentRecord.getId())) {
            throw new ServiceException("票据号不能为空");
        }

        // 设置为已确认状态
        paymentRecord.setPaymentStatus("1");

        // 消费账户变动
        balanceChange(paymentRecord);

        // 医疗保障金变动
        medicalSecurityFeeChange(paymentRecord);

        // 增值服务账单状态修改
        addedServiceBillStatusChange(paymentRecord);

        // 保存缴费单
        paymentRecord.setPaymentTime(DateUtils.getNowDate());
        paymentRecordMapper.insertPaymentRecord(paymentRecord);

        return 1;
    }

    /**
     * 暂存缴费单
     *
     * @param paymentRecord 缴费记录
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String draftPayment(PaymentRecord paymentRecord) {
        // 设置为暂存状态
        paymentRecord.setPaymentStatus("0");

        // 暂存时不触发账户变动，只保存基本信息
        paymentRecord.setPaymentTime(null); // 暂存时不设置缴费时间
        paymentRecord.setPaidDetails(null); // 暂存时不保存实缴详情
        paymentRecord.setTotalPaidCost(null); // 暂存时不计算实缴金额

        // 生成单号
        try {
            paymentRecord.setId(OrderUtils.getBillCode());
            paymentRecordMapper.insertPaymentRecord(paymentRecord);
        } catch (DuplicateKeyException e) {
            // 同步缓存中最新的单号
            String maxId = paymentRecordMapper.selectMaxId();
            if (maxId != null) {
                OrderUtils.setNumber(OrderUtils.BILL_ORDER2, Integer.parseInt(maxId.split("-")[2]));
            }
            paymentRecord.setId(OrderUtils.getBillCode());
            paymentRecordMapper.insertPaymentRecord(paymentRecord);
        }

        return paymentRecord.getId();
    }

    /**
     * 确认缴费单
     *
     * @param id          缴费记录ID
     * @param billNumber  票据号
     * @param paidDetails 实缴详情
     * @param remark      备注
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int confirmPayment(String id, String billNumber, List<PaymentRecord.PaidDetail> paidDetails, String remark) {
        // 查询暂存记录
        PaymentRecord draftRecord = paymentRecordMapper.selectPaymentRecordById(id);
        if (draftRecord == null) {
            throw new ServiceException("暂存记录不存在");
        }

        if (!"0".equals(draftRecord.getPaymentStatus())) {
            throw new ServiceException("只能确认暂存状态的缴费单");
        }

        // 验证票据号
        if (StrUtil.isBlank(billNumber)) {
            throw new ServiceException("票据号不能为空");
        }

        // 检查票据号是否已存在（查重）
        PaymentRecord existingRecord = paymentRecordMapper.selectPaymentRecordByBillNumber(billNumber);
        if (existingRecord != null && !existingRecord.getId().equals(id)) {
            throw new ServiceException("票据号已存在，请检查后重新输入");
        }

        // 验证实缴详情
        if (CollUtil.isEmpty(paidDetails)) {
            throw new ServiceException("实缴详情不能为空");
        }

        // 更新实缴信息
        draftRecord.setBillNumber(billNumber);
        draftRecord.setPaidDetails(paidDetails);
        draftRecord.setRemark(remark);
        draftRecord.setPaymentStatus("1");
        draftRecord.setPaymentTime(DateUtils.getNowDate());

        // 计算总实缴金额
        BigDecimal totalPaidCost = paidDetails.stream()
                .map(PaymentRecord.PaidDetail::getPaidCost)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        draftRecord.setTotalPaidCost(totalPaidCost);

        // 执行账户变动逻辑
        balanceChange(draftRecord);
        medicalSecurityFeeChange(draftRecord);
        addedServiceBillStatusChange(draftRecord);

        // 更新记录
        paymentRecordMapper.updatePaymentRecord(draftRecord);

        return 1;
    }

    /**
     * 修改暂存缴费单
     *
     * @param paymentRecord 缴费记录
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String updateDraftPayment(PaymentRecord paymentRecord) {
        // 查询现有记录
        PaymentRecord existingRecord = paymentRecordMapper.selectPaymentRecordById(paymentRecord.getId());
        if (existingRecord == null) {
            throw new ServiceException("缴费记录不存在");
        }

        if (!"0".equals(existingRecord.getPaymentStatus())) {
            throw new ServiceException("只能修改暂存状态的缴费单");
        }

        // 保持暂存状态
        paymentRecord.setPaymentStatus("0");
        paymentRecord.setPaymentTime(null); // 暂存时不设置缴费时间
        paymentRecord.setPaidDetails(null); // 暂存时不保存实缴详情
        paymentRecord.setTotalPaidCost(null); // 暂存时不计算实缴金额

        // 保存数据
        paymentRecordMapper.updatePaymentRecord(paymentRecord);

        return paymentRecord.getId();
    }

    /**
     * 删除暂存缴费单
     *
     * @param id 缴费记录ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteDraftPayment(String id) {
        // 查询记录
        PaymentRecord record = paymentRecordMapper.selectPaymentRecordById(id);
        if (record == null) {
            throw new ServiceException("缴费记录不存在");
        }

        if (!"0".equals(record.getPaymentStatus())) {
            throw new ServiceException("只能删除暂存状态的缴费单");
        }

        return paymentRecordMapper.deletePaymentRecordById(id);
    }

    /**
     * 医疗保障金变动处理
     *
     * @param paymentRecord 缴费记录
     */
    private void medicalSecurityFeeChange(PaymentRecord paymentRecord) {
        // 从缴费详情中找到医疗保障金项目
        Optional<PaymentRecord.Detail> medicalSecurityDetailOpt = paymentRecord.getDetails().stream()
                .filter(detail -> "medicalSecurityFee".equals(detail.getType()))
                .findFirst();

        if (!medicalSecurityDetailOpt.isPresent()) {
            return; // 如果没有医疗保障金项目，直接返回
        }

        PaymentRecord.Detail medicalSecurityDetail = medicalSecurityDetailOpt.get();
        BigDecimal paymentAmount = medicalSecurityDetail.getPaymentAmount();

        if (paymentAmount == null || paymentAmount.compareTo(BigDecimal.ZERO) == 0) {
            return; // 如果缴费金额为0或null，直接返回
        }

        String elderlyId = paymentRecord.getElderlyId();
        String operatorId = String.valueOf(SecurityUtils.getUserId());

        try {
            if (paymentAmount.compareTo(BigDecimal.ZERO) > 0) {
                // 正数表示充值保障金
                securityBalanceInfoService.doPay(elderlyId, operatorId, paymentAmount);
            } else {
                // 负数表示扣款保障金
                securityBalanceInfoService.doDeduction(elderlyId, operatorId, paymentAmount.abs());
            }
        } catch (Exception e) {
            log.error("医疗保障金变动处理失败，老人ID：{}，金额：{}", elderlyId, paymentAmount, e);
            throw new ServiceException("医疗保障金变动处理失败：" + e.getMessage());
        }
    }

    private void addedServiceBillStatusChange(PaymentRecord paymentRecord) {
        List<UserValueAddedServiceBill> billList = paymentRecord.getDetails().stream()
                .filter(detail -> ADDED_SERVICE.getCode().equals(detail.getType()))
                .map(PaymentRecord.Detail::getBillIds)
                .filter(StrUtil::isNotBlank)
                .map(billIds -> billIds.split(","))
                .flatMap(Arrays::stream)
                .map(String::trim)
                .filter(StrUtil::isNumeric)
                .map(Long::valueOf)
                .map(id -> {
                    UserValueAddedServiceBill bill = new UserValueAddedServiceBill();
                    bill.setId(id);
                    bill.setIsConfirm("1");
                    return bill;
                })
                .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(billList)) {
            userValueAddedServiceService.batchUpdateUserValueAddedServiceBill(billList);
        }
    }

    private void balanceChange(PaymentRecord paymentRecord) {
        // 计算实缴费用
        BigDecimal paidCostTotal = paymentRecord.getPaidDetails().stream()
                .map(PaymentRecord.PaidDetail::getPaidCost)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 计算本次账户变动额度 = 实缴费用 - 需缴费用 - 消费账户抵扣
        BigDecimal accountChangeCost = paidCostTotal
                .subtract(paymentRecord.getTotalCost())
                .subtract(Optional.ofNullable(paymentRecord.getDetails().stream()
                                .filter(d -> "accountDeduction".equals(d.getType()))
                                .findFirst()
                                .orElseGet(() -> {
                                    PaymentRecord.Detail newDetail = new PaymentRecord.Detail();
                                    newDetail.setPaymentAmount(BigDecimal.ZERO);
                                    return newDetail;
                                })
                                .getPaymentAmount())
                        .orElse(BigDecimal.ZERO));
        paymentRecord.setAccountAddCost(accountChangeCost);

        // 账户变动
        if (accountChangeCost.compareTo(BigDecimal.ZERO) > 0) { // 收入流程
            FeePaySaveVo feePaySaveVo = new FeePaySaveVo();
            feePaySaveVo.setUserId(paymentRecord.getElderlyId());
            feePaySaveVo.setPayAmount(accountChangeCost.abs());
            feePaySaveVo.setPayType("1");
            feePaySaveVo.setConsumeAccounType("1"); // 收入
            feePaySaveVo.setOperatorId(String.valueOf(SecurityUtils.getUserId()));
            feePayInfoService.insertFeePaySaveVo(feePaySaveVo, "4");
        } else if (accountChangeCost.compareTo(BigDecimal.ZERO) < 0) { // 支出流程
            balanceInfoService.doDeduction(paymentRecord.getElderlyId(), String.valueOf(SecurityUtils.getUserId()), accountChangeCost.abs(), "5");
        }

    }

    private void settlementBalanceChange(PaymentRecord paymentRecord) {
        // 获取消费账户金额
        BigDecimal accountChangeCost = Optional.ofNullable(paymentRecord.getDetails().stream()
                        .filter(d -> "accountDeduction".equals(d.getType()))
                        .findFirst()
                        .orElseGet(() -> {
                            PaymentRecord.Detail newDetail = new PaymentRecord.Detail();
                            newDetail.setPaymentAmount(BigDecimal.ZERO);
                            return newDetail;
                        })
                        .getPaymentAmount())
                .orElse(BigDecimal.ZERO);

        // 清空消费账户
        balanceInfoService.cleanBalance(paymentRecord.getElderlyId(), String.valueOf(SecurityUtils.getUserId()), accountChangeCost, "1");
    }

    /**
     * 结算时保障金变动处理
     *
     * @param paymentRecord 结算记录
     */
    private void settlementSecurityBalanceChange(PaymentRecord paymentRecord) {
        // 从结算详情中找到医疗保障金项目
        Optional<PaymentRecord.Detail> medicalSecurityDetailOpt = paymentRecord.getDetails().stream()
                .filter(detail -> "medicalSecurityFee".equals(detail.getType()))
                .findFirst();

        if (!medicalSecurityDetailOpt.isPresent()) {
            return; // 如果没有医疗保障金项目，直接返回
        }

        PaymentRecord.Detail medicalSecurityDetail = medicalSecurityDetailOpt.get();
        BigDecimal paymentAmount = medicalSecurityDetail.getPaymentAmount();

        String elderlyId = paymentRecord.getElderlyId();
        String operatorId = String.valueOf(SecurityUtils.getUserId());

        try {
            if (paymentAmount.compareTo(BigDecimal.ZERO) > 0) {
                // 正数表示充值保障金
                securityBalanceInfoService.doPay(elderlyId, operatorId, paymentAmount);
            } else {
                // 负数表示扣款保障金
                securityBalanceInfoService.doDeduction(elderlyId, operatorId, paymentAmount.abs());
            }
        } catch (Exception e) {
            log.error("医疗保障金变动处理失败，老人ID：{}，金额：{}", elderlyId, paymentAmount, e);
            throw new ServiceException("医疗保障金变动处理失败：" + e.getMessage());
        }
    }

    /**
     * 修改缴费确认单
     *
     * @param paymentRecord 缴费确认单
     * @return 结果
     */
    @Override
    public int updatePaymentRecord(PaymentRecord paymentRecord) {
        return paymentRecordMapper.updatePaymentRecord(paymentRecord);
    }

    /**
     * 批量删除缴费确认单
     *
     * @param ids 需要删除的缴费确认单主键
     * @return 结果
     */
    @Override
    public int deletePaymentRecordByIds(String[] ids) {
        return paymentRecordMapper.deletePaymentRecordByIds(ids);
    }

    /**
     * 删除缴费确认单信息
     *
     * @param id 缴费确认单主键
     * @return 结果
     */
    @Override
    public int deletePaymentRecordById(String id) {
        return paymentRecordMapper.deletePaymentRecordById(id);
    }

    @Override
    public PaymentRecord selectLastInfoByContractNumber(String contractNumber) {
        return paymentRecordMapper.selectLastInfoByContractNumber(contractNumber);
    }

    @Override
    public PaymentRecord generatePaymentInfo(String contractNumber) {
        // 获取基础信息
        PaymentRecordDTO paymentRecordDTO = contractInfoMapper.selectLiveInfo(contractNumber);
        if (paymentRecordDTO.getLiveState() == null) {
            throw new ServiceException("当前老人未入住");
        }

        // 重新计算请假日期和时长，过滤过去缴费单已计算过的请假日期和时长
        filterAlreadyChargedLeaveDatesAndDurations(paymentRecordDTO);

        // 获取当前合同上次缴费信息
        PaymentRecord lastPaymentRecord = this.selectLastInfoByContractNumber(contractNumber);

        // 构建缴费信息
        PaymentRecord paymentRecordRes = BeanUtil.copyProperties(paymentRecordDTO, PaymentRecord.class);
        paymentRecordRes.setContractNumber(contractNumber);
        paymentRecordRes.setDetails(new ArrayList<>());
        for (FeeType feeType : FeeType.values()) {
            PaymentRecord.Detail detail = buildDetail(feeType, lastPaymentRecord, paymentRecordDTO);
            paymentRecordRes.getDetails().add(detail);
        }

        return paymentRecordRes;
    }

    private void filterAlreadyChargedLeaveDatesAndDurations(PaymentRecordDTO paymentRecordDTO) {
        if (StrUtil.isBlank(paymentRecordDTO.getLeaveDates())) {
            // 如果当前DTO就没有请假日期，直接返回，总天数也应该已经是0或通过其他方式设置好了
            paymentRecordDTO.setLeaveDuration(0); // 确保天数为0
            return;
        }

        // 获取当前合同编号历史缴费单
        PaymentRecord params = new PaymentRecord();
        params.setContractNumber(paymentRecordDTO.getContractNumber());
        // 假设 selectPaymentRecordList 方法能获取到 PaymentRecord 对象的 leaveDates 字段
        List<PaymentRecord> list = selectPaymentRecordList(params);

        // 收集历史缴费单里所有已经处理过的请假周期 (只收集纯粹的日期范围部分)
        // 使用 Set 提高查找效率
        Set<String> alreadyChargedLeaveDateRanges = new HashSet<>();
        if (CollUtil.isNotEmpty(list)) {
            list.stream()
                    .filter(data -> !StrUtil.isBlank(data.getLeaveDates()) && data.getLeaveDuration() != null && data.getLeaveDuration() > 0)
                    .flatMap(data -> Arrays.stream(data.getLeaveDates().split("；")))
                    .map(this::extractPureDateRange) // <--- 使用辅助方法提取纯粹日期范围 (例如: "2025/04/02~2025/04/05")
                    .filter(dateRange -> !StrUtil.isBlank(dateRange)) // 过滤掉提取失败或空字符串
                    .forEach(alreadyChargedLeaveDateRanges::add);
        }


        // 过滤 paymentRecordDTO 当前请假周期，把已算过的移除 (基于纯粹的日期范围比对)
        List<String> currentLeaveDatesWithInfoList = Arrays.stream(paymentRecordDTO.getLeaveDates().split("；"))
                .collect(Collectors.toList()); // 先转为List，避免直接对split结果操作

        List<String> filteredLeaveDatesWithInfo = currentLeaveDatesWithInfoList.stream()
                .filter(dateRangeWithInfo -> {
                    String pureDateRange = extractPureDateRange(dateRangeWithInfo); // <--- 使用辅助方法提取纯粹日期范围
                    // 只有当纯粹日期范围不为空且未被包含在已处理集合中时，才保留这个带信息的日期段
                    return !StrUtil.isBlank(pureDateRange) && !alreadyChargedLeaveDateRanges.contains(pureDateRange);
                })
                .collect(Collectors.toList());

        // 把过滤后的结果重新设置回 DTO (保留原始带信息的格式)
        paymentRecordDTO.setLeaveDates(CollUtil.join(filteredLeaveDatesWithInfo, "；"));

        // 计算剩余的请假周期总天数 (基于过滤后的日期范围，解析纯粹日期部分)
        int totalDuration = 0;
        // 定义日期格式器，允许一位或两位数的月份和日期 (例如: "2025/4/2" 或 "2025/04/02")
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/M/d"); // <--- 关键修改在这里

        for (String dateRangeWithInfo : filteredLeaveDatesWithInfo) {
            String pureDateRange = extractPureDateRange(dateRangeWithInfo); // <--- 再次使用辅助方法提取纯粹日期范围

            if (StrUtil.isBlank(pureDateRange)) {
                log.warn("Skipping invalid leave date range format after extraction: {}", dateRangeWithInfo);
                continue; // 跳过无法解析的日期范围
            }

            String[] dates = pureDateRange.split("~"); // 对纯粹日期范围进行split
            if (dates.length == 2) {
                try {
                    // 对纯粹日期字符串进行解析，使用新的格式器
                    LocalDate startDate = LocalDate.parse(dates[0], formatter); // <--- 使用新的格式器
                    LocalDate endDate = LocalDate.parse(dates[1], formatter);   // <--- 使用新的格式器
                    long duration = ChronoUnit.DAYS.between(startDate, endDate) + 1; // 包括结束日期
                    totalDuration += duration;
                } catch (DateTimeParseException e) {
                    log.error("Error parsing pure leave date range: {} (Original: {})", pureDateRange, dateRangeWithInfo, e);
                    // 抛出异常
                    throw e;
                }
            } else {
                log.warn("Skipping date range with incorrect format after splitting pure range: {} (Original: {})", pureDateRange, dateRangeWithInfo);
            }
        }
        paymentRecordDTO.setLeaveDuration(totalDuration);
    }

    /**
     * 辅助方法：从带附加信息的日期范围字符串中提取纯粹的日期范围部分
     * 例如: "2025/4/10~2025/4/17(免除7天护理费，不免除餐费)" -> "2025/4/10~2025/4/17"
     * 如果没有括号，则返回原字符串
     */
    private String extractPureDateRange(String dateRangeWithInfo) {
        if (StrUtil.isBlank(dateRangeWithInfo)) {
            return "";
        }
        int openParenIndex = dateRangeWithInfo.indexOf('(');
        if (openParenIndex > 0) { // 找到 '(' 且不在第一个字符
            return dateRangeWithInfo.substring(0, openParenIndex).trim(); // 提取 '(' 之前的部分
        } else {
            return dateRangeWithInfo.trim(); // 没有括号，原样返回 (去除首尾空格)
        }
    }

    private PaymentRecord.Detail buildDetail(FeeType feeType, PaymentRecord lastPaymentRecord, PaymentRecordDTO dto) {
        PaymentRecord.Detail detail = new PaymentRecord.Detail();
        detail.setType(feeType.getCode());
        detail.setPaymentAmount(BigDecimal.ZERO);
        PaymentRecord.PrepaidPeriod prepaidPeriod = new PaymentRecord.PrepaidPeriod();
        prepaidPeriod.setFull(0);
        detail.setPrepaidPeriod(prepaidPeriod);

        switch (feeType) {
            case BED_FEE:
                detail.setTypeName(feeType.getDescription() + "（" + dto.getRoomTypeName() + "）");
                detail.setFeeStandard(dto.getRoomCost());
                prepaidPeriod.setDiscount(new BigDecimal(dto.getBedDiscount() == null ? "1" : dto.getBedDiscount()));

                // 计算预估到期日期并获取最新变更单ID
                FeeCalculationResult bedFeeResult = calculateFeeExpiryAndChangeId(feeType, lastPaymentRecord, dto);
                detail.setEstimatedExpiryDate(bedFeeResult.getEstimatedExpiryDate());
                detail.setChangePaymentId(bedFeeResult.getLatestChangeRecordId());
                break;

            case BEDDING_FEE:
                detail.setTypeName(feeType.getDescription());
                detail.setFeeStandard(new BigDecimal("500"));
                break;

            case CARE_FEE:
                detail.setTypeName(feeType.getDescription() + "（" + dto.getComboName() + "）");
                detail.setFeeStandard(dto.getComboCost());

                // 计算预估到期日期并获取最新变更单ID
                FeeCalculationResult careFeeResult = calculateFeeExpiryAndChangeId(feeType, lastPaymentRecord, dto);
                detail.setEstimatedExpiryDate(careFeeResult.getEstimatedExpiryDate());
                detail.setChangePaymentId(careFeeResult.getLatestChangeRecordId());
                break;

            case MEAL_FEE:
                detail.setTypeName(feeType.getDescription() + "（" + dto.getMealName() + "）");
                detail.setFeeStandard(dto.getMealCost());

                // 计算预估到期日期并获取最新变更单ID
                FeeCalculationResult mealFeeResult = calculateFeeExpiryAndChangeId(feeType, lastPaymentRecord, dto);
                detail.setEstimatedExpiryDate(mealFeeResult.getEstimatedExpiryDate());
                detail.setChangePaymentId(mealFeeResult.getLatestChangeRecordId());
                break;

            case AC_FEE:
                detail.setTypeName(feeType.getDescription());
                detail.setFeeStandard(new BigDecimal("500"));
                detail.setEstimatedExpiryDate(calculateStartDate(lastPaymentRecord, dto.getContractStartDate(), feeType));
                break;

            case ADDED_SERVICE:
                detail.setTypeName(ADDED_SERVICE.getDescription());
                break;

            case MEDICAL_SECURITY_FEE:
                detail.setTypeName(feeType.getDescription());
                detail.setFeeStandard(calculateMedicalSecurityFee(lastPaymentRecord)); // 本次feeStandard =  上次医疗保险金feeStandard + paymentAmount
                break;

            case ACCOUNT_DEDUCTION:
                detail.setTypeName(feeType.getDescription());
                detail.setFeeStandard(dto.getAccountBalance() == null ? BigDecimal.ZERO : dto.getAccountBalance());
                break;

            default:
                throw new IllegalArgumentException("Invalid fee type: " + feeType);
        }

        return detail;
    }

    /**
     * 计算指定费用类型的预估到期日期，并获取与该合同及该费用类型关联的最新缴费变更单ID。
     * <p>
     * 逻辑步骤：
     * 1. 查询指定合同编号的所有历史缴费变更单记录，并按ID倒序排列。
     * 2. 从这些变更单的详情中，提取出与当前{@code feeType}相关的、第一个有效的（非null）"变更结束日期"（endDate）。
     * 这个日期通常来自最新的一条包含该费用类型变更信息的变更单。
     * 3. 获取按ID倒序排列后的第一条变更单的ID，如果该变更单的详情中包含当前{@code feeType}的变更，
     * 则将其ID作为"最新的（与费用类型相关的）变更单ID"。否则，此ID为null。
     * (注意：这里的逻辑是，如果最新的变更单不包含当前费用类型的变更，那么changePaymentId应该为null，
     * 或者沿用上次缴费的changePaymentId。当前实现是如果最新变更单不含该feeType，则relatedChangeRecordId为null)
     * 4. 检查是否存在上一次的缴费记录 (lastPaymentRecord)。
     * a. 如果存在上次缴费记录，并且其中包含与当前{@code feeType}对应的缴费详情：
     * i. 获取上次该费用类型详情中记录的 changePaymentId。
     * ii. 如果步骤3中获取的"最新的（与费用类型相关的）变更单ID"为null，或者它与上次记录的changePaymentId相同，
     * 则预估到期日沿用上次该费用类型的预估到期日。
     * iii. 如果"最新的（与费用类型相关的）变更单ID"与上次记录的changePaymentId不同（表示发生了新的相关变更）：
     * - 如果步骤2中获取的"变更结束日期"有效，则预估到期日设为这个"变更结束日期"。
     * - 如果"变更结束日期"无效，则预估到期日仍沿用上次该费用类型的预估到期日作为回退。
     * b. 如果存在上次缴费记录，但没有当前{@code feeType}的详情：
     * - 如果步骤2中获取的"变更结束日期"有效，则预估到期日设为这个"变更结束日期"。
     * - 否则，预估到期日设为合同的开始日期。
     * c. 如果不存在上次缴费记录（例如首次缴费）：
     * - 如果步骤2中获取的"变更结束日期"有效，则预估到期日设为这个"变更结束日期"。
     * - 否则，预估到期日设为合同的开始日期。
     * 5. 对最终计算出的预估到期日进行null检查，如果仍为null，则默认使用合同开始日期。
     * 6. 返回一个包含最终预估到期日和"最新的（与费用类型相关的）变更单ID"的结果对象。
     * </p>
     *
     * @param feeType           当前正在处理的费用类型 (FeeType枚举)。
     * @param lastPaymentRecord 上一次的缴费记录对象，可能为null。
     * @param dto               包含合同基本信息（如合同开始日期、合同编号）的PaymentRecordDTO对象。
     * @return FeeCalculationResult 对象，其中包含了计算得出的预估到期日 (estimatedExpiryDate)
     * 和与该费用类型相关的最新缴费变更单ID (latestChangeRecordId)。
     */
    private FeeCalculationResult calculateFeeExpiryAndChangeId(FeeType feeType, PaymentRecord lastPaymentRecord, PaymentRecordDTO dto) {
        // 1. 查询指定合同的所有历史变更单，按ID倒序（最新的在前）
        List<PaymentChangeRecord> historicalChanges = paymentChangeRecordMapper.selectByContractNumberOrderByIdDesc(dto.getContractNumber());

        // 2. 从历史变更单中提取与当前 feeType 相关的第一个有效的"变更结束日期" (endDate)
        Date feeTypeSpecificLatestChangeEndDate = historicalChanges.stream()
                .filter(Objects::nonNull)
                .filter(change -> CollUtil.isNotEmpty(change.getDetails()))
                .flatMap(change -> change.getDetails().stream()
                        // 筛选出与当前 feeType 相关的 detail
                        .filter(detail -> feeType.getCode().equals(detail.getType()))
                        .map(PaymentChangeRecord.Detail::getEndDate) // 获取该 detail 的 endDate
                )
                .filter(Objects::nonNull) // 过滤掉为 null 的 endDate
                .findFirst() // 获取流中的第一个（即来自最新相关变更单的第一个有效endDate）
                .orElse(null);

        // 3. 获取最新的、且包含当前 feeType 变更、并且该变更详情中 endDate 不为空的变更单ID
        String feeTypeSpecificLatestChangeRecordId = historicalChanges.stream()
                .filter(change -> Objects.nonNull(change) && CollUtil.isNotEmpty(change.getDetails())) // 确保变更单和其details有效
                .filter(change -> change.getDetails().stream() // 进一步检查details内容
                        .anyMatch(detail ->
                                feeType.getCode().equals(detail.getType()) && // 1. 类型匹配
                                        detail.getEndDate() != null                   // 2. 对应的endDate不为null
                        )
                )
                .findFirst() // 获取第一个符合上述所有条件的变更单（即最新的那一个）
                .map(PaymentChangeRecord::getId) // 获取其ID
                .orElse(null); // 如果没有找到符合条件的，则为null

        Date estimatedExpiryDate; // 用于存储计算出的预估到期日

        // 4. 根据是否存在上次缴费记录以及变更情况来确定预估到期日
        if (lastPaymentRecord != null && CollUtil.isNotEmpty(lastPaymentRecord.getDetails())) {
            Optional<PaymentRecord.Detail> lastFeeTypeDetailOpt = lastPaymentRecord.getDetails().stream()
                    .filter(d -> feeType.getCode().equals(d.getType())) // 筛选出上次缴费记录中对应 feeType 的详情
                    .findFirst();

            if (lastFeeTypeDetailOpt.isPresent()) {
                PaymentRecord.Detail lastFeeTypeDetail = lastFeeTypeDetailOpt.get();
                String lastAssociatedChangeId = lastFeeTypeDetail.getChangePaymentId();

                if (feeTypeSpecificLatestChangeRecordId == null || feeTypeSpecificLatestChangeRecordId.equals(lastAssociatedChangeId)) {
                    estimatedExpiryDate = lastFeeTypeDetail.getEstimatedExpiryDate();
                } else {
                    estimatedExpiryDate = (feeTypeSpecificLatestChangeEndDate != null) ? feeTypeSpecificLatestChangeEndDate : lastFeeTypeDetail.getEstimatedExpiryDate();
                }
            } else {
                estimatedExpiryDate = (feeTypeSpecificLatestChangeEndDate != null) ? feeTypeSpecificLatestChangeEndDate : dto.getContractStartDate();
            }
        } else {
            estimatedExpiryDate = (feeTypeSpecificLatestChangeEndDate != null) ? feeTypeSpecificLatestChangeEndDate : dto.getContractStartDate();
        }

        // 5. 最终的null检查和日期计算
        Date finalEstimatedExpiryDate = (estimatedExpiryDate != null) ? estimatedExpiryDate : dto.getContractStartDate();
        if (!finalEstimatedExpiryDate.equals(dto.getContractStartDate())) {
            finalEstimatedExpiryDate = DateUtil.offsetDay(finalEstimatedExpiryDate, 1);
        }

        // 6. 返回结果
        return new FeeCalculationResult(finalEstimatedExpiryDate, feeTypeSpecificLatestChangeRecordId);
    }

    /**
     * 定义内部类或单独的类
     */
    private static class FeeCalculationResult { // 名称改为通用
        private final Date estimatedExpiryDate;
        private final String latestChangeRecordId; // 特定于该费用类型的最新变更单ID (如果适用)

        public FeeCalculationResult(Date estimatedExpiryDate, String latestChangeRecordId) {
            this.estimatedExpiryDate = estimatedExpiryDate;
            this.latestChangeRecordId = latestChangeRecordId;
        }

        public Date getEstimatedExpiryDate() {
            return estimatedExpiryDate;
        }

        public String getLatestChangeRecordId() {
            return latestChangeRecordId;
        }
    }

    private BigDecimal calculateMedicalSecurityFee(PaymentRecord lastPaymentRecord) {
        if (lastPaymentRecord == null) {
            return BigDecimal.ZERO;
        }

        for (PaymentRecord.Detail lastDetail : lastPaymentRecord.getDetails()) {
            if (lastDetail.getType().equals(FeeType.MEDICAL_SECURITY_FEE.getCode())) {
                return lastDetail.getFeeStandard().add(lastDetail.getPaymentAmount());
            }
        }

        return BigDecimal.ZERO;
    }

    private BigDecimal getDiscountByContractCycle(Integer contractCycle) {
        if (contractCycle == null) {
            return BigDecimal.ONE;
        }

        if (contractCycle >= 3 && contractCycle < 6) {
            return new BigDecimal("0.6");
        } else if (contractCycle >= 6 && contractCycle < 12) {
            return new BigDecimal("0.7");
        } else if (contractCycle >= 12) {
            return new BigDecimal("0.8");
        } else {
            return BigDecimal.ONE;
        }
    }

    /**
     * 计算预估到期日期
     *
     * @param lastPaymentRecord
     * @param contractStartDate
     * @param feeType
     * @return
     */
    @Override
    public Date calculateStartDate(PaymentRecord lastPaymentRecord, Date contractStartDate, FeeType feeType) {
        if (lastPaymentRecord == null) {
            return contractStartDate;
        }

        for (PaymentRecord.Detail lastDetail : lastPaymentRecord.getDetails()) {
            if (lastDetail.getType().equals(feeType.getCode())) {
                return lastDetail.getEstimatedExpiryDate();
            }
        }

        return contractStartDate;
    }

    @Override
    public List<PaymentRemindResp> paymentRemindList(PaymentRecord paymentRecord) {
        return paymentRecordMapper.paymentRemindList(paymentRecord);
    }

    @Override
    public PaymentRecord generatePaymentSettlementInfo(String contractNumber) {
        // 1. 获取基础信息
        PaymentRecordDTO paymentRecordDTO = contractInfoMapper.selectLiveInfo(contractNumber);
        if (paymentRecordDTO == null || paymentRecordDTO.getLiveState() == null) { // 增加对 paymentRecordDTO 的 null 检查
            throw new ServiceException("当前老人未入住或信息获取失败");
        }

        // 2.重新计算请假日期和时长，过滤过去缴费单已计算过的请假日期和时长
        filterAlreadyChargedLeaveDatesAndDurations(paymentRecordDTO);

        // 3. 查询该合同的所有历史缴费记录 (PaymentRecord)
        List<PaymentRecord> historicalPaymentRecords = fetchPaymentRecords(contractNumber); // 全部的历史缴费记录
        if (historicalPaymentRecords.isEmpty()) { // 使用 CollUtil.isEmpty 更佳
            throw new ServiceException("此合同没有缴费记录，无法生成结算单");
        }

        // 4. 获取最近一条缴费记录作为结算单基本信息 (这里的 lastPaymentRecord 主要是为了获取其 details 结构和一些默认值)
        PaymentRecord lastPaymentRecord = getLastPaymentRecord(historicalPaymentRecords);

        // 创建一个新的 PaymentRecord 对象用于结算单，避免直接修改上次缴费记录
        PaymentRecord settlementRecord = new PaymentRecord();
        // 从 lastPaymentRecord 复制一些基础属性，但 details, totalCost, paidDetails, paymentTime, id, feeType 会被重置或重新计算
        BeanUtil.copyProperties(lastPaymentRecord, settlementRecord, "details", "totalCost", "paidDetails", "paymentTime", "id", "feeType");
        settlementRecord.setId(null); // 结算单ID通常在保存时生成或由外部传入
        settlementRecord.setFeeType("2"); // 标记为结算单
        settlementRecord.setPaymentTime(null); // 结算操作时间在执行结算时设置
        settlementRecord.setDetails(new ArrayList<>()); // 初始化 details 列表
        settlementRecord.setLeaveDates(paymentRecordDTO.getLeaveDates()); // 使用最新的请假信息
        settlementRecord.setLeaveDuration(paymentRecordDTO.getLeaveDuration());
        settlementRecord.setDischargeDate(new Date()); // 离院日期，默认为当前日期

        // 5. 计算各项费用历史总额 (这个 totalFees 仍用于非床/护/餐的费用项的"历史已缴"参考)
        Map<String, BigDecimal> totalFeesForAllHistory = calculateTotalFees(historicalPaymentRecords);

        // 新增：查询该合同的所有历史变更单记录 (PaymentChangeRecord)，按ID倒序排列 (最新的在前)
        List<PaymentChangeRecord> historicalChangeRecords = paymentChangeRecordMapper.selectByContractNumberOrderByIdDesc(contractNumber);

        // 6. 填充结算单详情
        // 将 historicalPaymentRecords (所有历史缴费) 和 historicalChangeRecords 传递进去
        // 也传入 paymentRecordDTO 用于获取合同开始日期等默认值
        fillSettlementDetails(settlementRecord, lastPaymentRecord, totalFeesForAllHistory, historicalPaymentRecords, historicalChangeRecords, paymentRecordDTO);

        // 7. 清理或重置结算单顶层的不适用字段
        settlementRecord.setTotalCost(BigDecimal.ZERO); // 结算单的 totalCost 通常是根据详情计算得出的应退/应补总额
        settlementRecord.setPaidDetails(Collections.emptyList()); // 结算单不涉及新的支付方式构成

        return settlementRecord;
    }

    @Override
    public List<FeeStatisticsResp> feeStatistics(FeeStatisticsReq feeStatisticsReq) {
        return paymentRecordMapper.feeStatistics(feeStatisticsReq);
    }

    /**
     * 查询缴费记录
     *
     * @param contractNumber
     * @return
     */
    @Override
    public List<PaymentRecord> fetchPaymentRecords(String contractNumber) {
        PaymentRecord query = new PaymentRecord();
        query.setContractNumber(contractNumber);
        query.setFeeType("1"); // 缴费
        return paymentRecordMapper.selectPaymentRecordList(query);
    }

    /**
     * 获取最新缴费记录
     *
     * @param records
     * @return
     */
    @Override
    public PaymentRecord getLastPaymentRecord(List<PaymentRecord> records) {
        return records.stream()
                .max(Comparator.comparing(PaymentRecord::getPaymentTime))
                .orElseThrow(() -> new ServiceException("未找到缴费记录"));
    }

    /**
     * 计算费用总额
     *
     * @param records
     * @return
     */
    @Override
    public Map<String, BigDecimal> calculateTotalFees(List<PaymentRecord> records) {
        Map<String, BigDecimal> totals = new HashMap<>();
        for (PaymentRecord record : records) {
            for (PaymentRecord.Detail detail : record.getDetails()) {
                String type = detail.getType();
                BigDecimal amount = Optional.ofNullable(detail.getPaymentAmount()).orElse(BigDecimal.ZERO);
                totals.merge(type, amount, BigDecimal::add);
            }
        }
        return totals;
    }

    /**
     * 暂存结算单
     *
     * @param paymentRecord 结算记录
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String draftSettlement(PaymentRecord paymentRecord) {
        // 设置为暂存状态
        paymentRecord.setPaymentStatus("0");
        paymentRecord.setFeeType("2"); // 结算单

        // 暂存时不触发账户变动，只保存基本信息
        paymentRecord.setPaymentTime(null); // 暂存时不设置结算时间
        paymentRecord.setPaidDetails(null); // 暂存时不保存实缴详情
        paymentRecord.setTotalPaidCost(null); // 暂存时不计算实缴金额

        // 生成ID并保存
        try {
            paymentRecord.setId(OrderUtils.getBillCode());
            paymentRecordMapper.insertPaymentRecord(paymentRecord);
        } catch (DuplicateKeyException e) {
            // 同步缓存中最新的单号
            String maxId = paymentRecordMapper.selectMaxId();
            if (maxId != null) {
                OrderUtils.setNumber(OrderUtils.BILL_ORDER2, Integer.parseInt(maxId.split("-")[2]));
            }
            paymentRecord.setId(OrderUtils.getBillCode());
            paymentRecordMapper.insertPaymentRecord(paymentRecord);
        }

        return paymentRecord.getId();
    }

    /**
     * 确认结算单
     *
     * @param id          结算记录ID
     * @param billNumber  票据号
     * @param paidDetails 实缴详情
     * @param remark      备注
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int confirmSettlement(String id, String billNumber, List<PaymentRecord.PaidDetail> paidDetails, String remark) {
        // 查询暂存记录
        PaymentRecord draftRecord = paymentRecordMapper.selectPaymentRecordById(id);
        if (draftRecord == null) {
            throw new ServiceException("暂存记录不存在");
        }

        if (!"0".equals(draftRecord.getPaymentStatus())) {
            throw new ServiceException("只能确认暂存状态的结算单");
        }

        // 验证票据号
        if (StrUtil.isBlank(billNumber)) {
            throw new ServiceException("票据号不能为空");
        }

        // 检查票据号是否已存在（查重）
        PaymentRecord existingRecord = paymentRecordMapper.selectPaymentRecordByBillNumber(billNumber);
        if (existingRecord != null && !existingRecord.getId().equals(id)) {
            throw new ServiceException("票据号已存在，请检查后重新输入");
        }

        // 更新票据号和其他信息
        draftRecord.setBillNumber(billNumber);
        draftRecord.setPaidDetails(paidDetails);
        draftRecord.setRemark(remark);
        draftRecord.setPaymentStatus("1"); // 设置为已确认状态

        // 计算实缴总金额
        if (CollUtil.isNotEmpty(paidDetails)) {
            BigDecimal totalPaidCost = paidDetails.stream()
                    .map(PaymentRecord.PaidDetail::getPaidCost)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            draftRecord.setTotalPaidCost(totalPaidCost);
        }

        // 账户变动
        settlementBalanceChange(draftRecord);

        // 保障金变动处理
        settlementSecurityBalanceChange(draftRecord);

        // 增值服务账单状态修改
        addedServiceBillStatusChange(draftRecord);

        // 保存结算单
        draftRecord.setPaymentTime(DateUtils.getNowDate());
        paymentRecordMapper.updatePaymentRecord(draftRecord);

        // 退住
        endLive(draftRecord.getElderlyId());

        return 1;
    }

    /**
     * 修改暂存结算单
     *
     * @param paymentRecord 结算记录
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String updateDraftSettlement(PaymentRecord paymentRecord) {
        // 查询现有记录
        PaymentRecord existingRecord = paymentRecordMapper.selectPaymentRecordById(paymentRecord.getId());
        if (existingRecord == null) {
            throw new ServiceException("结算记录不存在");
        }

        if (!"0".equals(existingRecord.getPaymentStatus())) {
            throw new ServiceException("只能修改暂存状态的结算单");
        }

        // 保持暂存状态
        paymentRecord.setPaymentStatus("0");
        paymentRecord.setFeeType("2"); // 结算单
        paymentRecord.setPaymentTime(null); // 暂存时不设置结算时间
        paymentRecord.setPaidDetails(null); // 暂存时不保存实缴详情
        paymentRecord.setTotalPaidCost(null); // 暂存时不计算实缴金额

        // 保存数据
        paymentRecordMapper.updatePaymentRecord(paymentRecord);

        return paymentRecord.getId();
    }

    /**
     * 删除暂存结算单
     *
     * @param id 结算记录ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteDraftSettlement(String id) {
        // 查询记录
        PaymentRecord record = paymentRecordMapper.selectPaymentRecordById(id);
        if (record == null) {
            throw new ServiceException("结算记录不存在");
        }

        if (!"0".equals(record.getPaymentStatus())) {
            throw new ServiceException("只能删除暂存状态的结算单");
        }

        return paymentRecordMapper.deletePaymentRecordById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int settlement(PaymentRecord paymentRecord) {
        // 设置为已确认状态
        paymentRecord.setPaymentStatus("1");

        // 账户变动
        settlementBalanceChange(paymentRecord);

        // 保障金变动处理
        settlementSecurityBalanceChange(paymentRecord);

        // 增值服务账单状态修改
        addedServiceBillStatusChange(paymentRecord);

        // 保存结算单
        paymentRecord.setPaymentTime(DateUtils.getNowDate());
        try {
            paymentRecord.setId(OrderUtils.getBillCode());
            paymentRecordMapper.insertPaymentRecord(paymentRecord);
        } catch (DuplicateKeyException e) {
            // 同步缓存中最新的单号
            String maxId = paymentRecordMapper.selectMaxId();
            if (maxId != null) {
                OrderUtils.setNumber(OrderUtils.BILL_ORDER2, Integer.parseInt(maxId.split("-")[2]));
            }
            paymentRecord.setId(OrderUtils.getBillCode());
            return paymentRecordMapper.insertPaymentRecord(paymentRecord);
        }

        // 退住
        endLive(paymentRecord.getElderlyId());

        return 1;
    }

    private void endLive(String elderlyId) {
        // 查询老人当前liveid
        LiveQueryVo liveQueryVo = new LiveQueryVo();
        liveQueryVo.setUserId(elderlyId);
        liveQueryVo.setState("0");
        List<LiveInfoRes> liveInfos = liveBaseInfoService.selectLiveBaseInfoList(liveQueryVo);
        if (CollUtil.isEmpty(liveInfos)) {
            throw new ServiceException("该老人没有入住或状态不为入住中");
        }
        if (liveInfos.size() > 1) {
            throw new ServiceException("该老人入住记录不唯一");
        }

        String liveId = liveInfos.get(0).getId();

        // 修改当前入住记录下 在住床位记录状态
        LiveBedRecords param = new LiveBedRecords();
        param.setLiveId(liveId);
        param.setLiveState("0");
        List<LiveBedRecords> liveBedRecords = liveBedRecordsService.selectLiveBedRecordsList(param);
        for (LiveBedRecords liveBedRecord : liveBedRecords) {
            liveBedRecord.setEndDate(DateUtils.getNowDate());
            liveBedRecord.setLiveState("2");
            liveBedRecordsService.updateLiveBedRecords(liveBedRecord);
        }

        // 修改当前入住记录下 在用的套餐记录
        LiveComboRecords comboParam = new LiveComboRecords();
        comboParam.setState("0"); // 在用
        comboParam.setLiveId(liveId);
        List<LiveComboRecords> liveComboRecords = liveComboRecordsService.selectLiveComboRecordsList(comboParam);

        for (LiveComboRecords liveComboRecord : liveComboRecords) {
            liveComboRecord.setState("2"); // 终止
        }

        // 更新居住base信息
        LiveBaseInfo liveBaseInfo = new LiveBaseInfo();
        liveBaseInfo.setState("3");
        liveBaseInfo.setId(liveId);
        liveBaseInfo.setUpdateBy(SecurityUtils.getUserId().toString());
        liveBaseInfo.setUpdateTime(new Date());
        liveBaseInfoMapper.updateLiveBaseInfo(liveBaseInfo);

        // 更新老人信息
        ElderlyPeopleInfo elderlyPeopleInfo = new ElderlyPeopleInfo();
        elderlyPeopleInfo.setId(elderlyId);
        elderlyPeopleInfo.setStatus("3");
        elderlyPeopleInfoService.updateElderlyPeopleInfo(elderlyPeopleInfo);
    }


    /**
     * 填充结算单详情。
     * 对于床位费、护理费、餐费，其 changeDate 和 paidAmount 将根据特定逻辑计算。
     * 其他费用项的 paidAmount 通常取自历史总额。
     *
     * @param settlementRecordToFill   要填充的结算单对象 (其details列表会被填充)
     * @param lastActualPaymentRecord  最近一次实际缴费的记录 (用于获取结构和某些默认值)
     * @param totalFeesForAllHistory   所有历史缴费中各项费用的总额 (用于非床/护/餐费用项)
     * @param historicalPaymentRecords 该合同的所有历史缴费记录
     * @param historicalChangeRecords  该合同的所有历史变更单记录 (应按ID倒序)
     * @param dto                      当前的PaymentRecordDTO (用于获取合同开始日期等默认值)
     */
    private void fillSettlementDetails(PaymentRecord settlementRecordToFill,
                                       PaymentRecord lastActualPaymentRecord,
                                       Map<String, BigDecimal> totalFeesForAllHistory,
                                       List<PaymentRecord> historicalPaymentRecords,
                                       List<PaymentChangeRecord> historicalChangeRecords,
                                       PaymentRecordDTO dto) {

        // 确定结算单中需要包含的费用类型
        Set<String> feeTypesInSettlement = new LinkedHashSet<>();
        if (lastActualPaymentRecord != null && CollUtil.isNotEmpty(lastActualPaymentRecord.getDetails())) {
            lastActualPaymentRecord.getDetails().forEach(d -> feeTypesInSettlement.add(d.getType()));
        }
        feeTypesInSettlement.add(FeeType.BED_FEE.getCode());
        feeTypesInSettlement.add(FeeType.BEDDING_FEE.getCode());
        feeTypesInSettlement.add(FeeType.CARE_FEE.getCode());
        feeTypesInSettlement.add(FeeType.MEAL_FEE.getCode());
        feeTypesInSettlement.add(FeeType.AC_FEE.getCode());
        feeTypesInSettlement.add(FeeType.ADDED_SERVICE.getCode());
        feeTypesInSettlement.add(FeeType.MEDICAL_SECURITY_FEE.getCode());
        feeTypesInSettlement.add(FeeType.ACCOUNT_DEDUCTION.getCode());

        for (String typeCode : feeTypesInSettlement) {
            FeeType feeType = FeeType.fromCode(typeCode);
            if (feeType == null) continue;

            PaymentRecord.Detail detail = new PaymentRecord.Detail();
            detail.setType(feeType.getCode());
            detail.setTypeName(feeType.getDescription());
            detail.setPaymentAmount(BigDecimal.ZERO);
            detail.setRefundAmount(BigDecimal.ZERO);
            detail.setPaidAmount(null);
            detail.setEstimatedExpiryDate(null);
            detail.setRemarks(null);
            // 对于结算单，PrepaidPeriod 通常不直接从上次复制，除非是床位费的折扣信息
            // 其他类型的 PrepaidPeriod 在结算时通常是根据退费规则新生成的，或者不展示
            detail.setPrepaidPeriod(null); // 默认不设置PrepaidPeriod

            Optional<PaymentRecord.Detail> lastMatchingDetailOpt = Optional.empty();
            if (lastActualPaymentRecord != null && CollUtil.isNotEmpty(lastActualPaymentRecord.getDetails())) {
                lastMatchingDetailOpt = lastActualPaymentRecord.getDetails().stream()
                        .filter(d -> feeType.getCode().equals(d.getType()))
                        .findFirst();
            }

            if (lastMatchingDetailOpt.isPresent()) {
                PaymentRecord.Detail lastDetail = lastMatchingDetailOpt.get();
                detail.setFeeStandard(lastDetail.getFeeStandard());
                detail.setTypeName(lastDetail.getTypeName());
                if (feeType == FeeType.BED_FEE && lastDetail.getPrepaidPeriod() != null) {
                    // 仅为床位费复制折扣信息到新的PrepaidPeriod对象
                    PaymentRecord.PrepaidPeriod pp = new PaymentRecord.PrepaidPeriod();
                    pp.setDiscount(lastDetail.getPrepaidPeriod().getDiscount());
                    // 如果还有其他床位费特有的PrepaidPeriod属性需要保留，也在这里设置
                    detail.setPrepaidPeriod(pp);
                }
                // 对于其他费用类型 (护理费、餐费、空调费等)，不再从上次缴费复制PrepaidPeriod
                // 如果结算时需要展示与预付相关的退费信息，那部分逻辑会在计算退补时独立处理，
                // 可能会新创建一个PrepaidPeriod对象来承载这些退费信息。

            } else {
                switch (feeType) {
                    case BED_FEE:
                        detail.setFeeStandard(dto.getRoomCost());
                        detail.setTypeName(feeType.getDescription() + "（" + dto.getRoomTypeName() + "）");
                        break;
                    case BEDDING_FEE:
                        detail.setFeeStandard(new BigDecimal("500"));
                        break;
                    case CARE_FEE:
                        detail.setFeeStandard(dto.getComboCost());
                        detail.setTypeName(feeType.getDescription() + "（" + dto.getComboName() + "）");
                        break;
                    case MEAL_FEE:
                        detail.setFeeStandard(dto.getMealCost());
                        detail.setTypeName(feeType.getDescription() + "（" + dto.getMealName() + "）");
                        break;
                    case AC_FEE:
                        detail.setFeeStandard(new BigDecimal("500"));
                        break;
                    default:
                        if (detail.getFeeStandard() == null) {
                            detail.setFeeStandard(BigDecimal.ZERO);
                        }
                        break;
                }
            }

            if (feeType == FeeType.BED_FEE || feeType == FeeType.CARE_FEE || feeType == FeeType.MEAL_FEE) {
                calculateChangeDateAndPaidAmountForSettlementDetail(detail, feeType, dto, historicalPaymentRecords, historicalChangeRecords);
            } else {
                detail.setEstimatedExpiryDate(null);
                switch (feeType) {
                    case BEDDING_FEE:
                        // 床品费结算时设置已缴费金额为历史总额
                        detail.setPaidAmount(totalFeesForAllHistory.getOrDefault(feeType.getCode(), BigDecimal.ZERO));
                        break;
                    case AC_FEE:
                        // 空调费结算时的 PrepaidPeriod (如果需要显示退费周期) 会在计算退补时生成
                        detail.setPaidAmount(totalFeesForAllHistory.getOrDefault(feeType.getCode(), BigDecimal.ZERO));
                        break;
                    case ADDED_SERVICE:
                        fetchUnpaidValueAddedBillsWithDetails(detail, lastActualPaymentRecord.getElderlyId()); // 查询未缴费的增值服务账单，并填充detail
                        break;
                    case MEDICAL_SECURITY_FEE:
                        // 获取保障金账户余额
                        BigDecimal securityBalance = fetchSecurityBalance(settlementRecordToFill.getElderlyId());
                        detail.setFeeStandard(securityBalance); // 显示当前保障金余额
                        detail.setPaymentAmount(securityBalance); // 退还全部保障金
                        break;
                    case ACCOUNT_DEDUCTION:
                        detail.setFeeStandard(fetchUserBalance(settlementRecordToFill.getElderlyId()));
                        detail.setPaymentAmount(detail.getFeeStandard());
                        break;
                    default:
                        break;
                }
            }
            settlementRecordToFill.getDetails().add(detail);
        }
    }

    /**
     * 为结算单中的特定费用项（床位费、护理费、餐费）计算"最近变更开始日期"(存储在detailToUpdate的changeDate字段)
     * 和"（基于该最新变更的）已缴费金额"(paidAmount)。
     *
     * @param detailToUpdate           要更新的 PaymentRecord.Detail 对象
     * @param feeType                  费用类型
     * @param dto                      PaymentRecordDTO (当前在此方法中未使用，但保留签名以备将来扩展或获取默认值)
     * @param historicalPaymentRecords 所有历史缴费记录
     * @param historicalChangeRecords  所有历史变更单记录 (应已按ID倒序)
     */
    private void calculateChangeDateAndPaidAmountForSettlementDetail(PaymentRecord.Detail detailToUpdate,
                                                                     FeeType feeType,
                                                                     PaymentRecordDTO dto,
                                                                     List<PaymentRecord> historicalPaymentRecords,
                                                                     List<PaymentChangeRecord> historicalChangeRecords) {

        // 1. 查找与 feeType 相关且 endDate 有效的最新变更单信息
        // 使用 Optional 链来获取 associatedChangeRecordId 和 latestValidChangeEndDate
        // 这样它们在声明时就可以被认为是 final (effectively final)
        final Optional<PaymentChangeRecord> latestRelevantChangeOpt = Optional.ofNullable(historicalChangeRecords)
                .filter(CollUtil::isNotEmpty) // 确保列表不为空
                .flatMap(changes -> changes.stream() // flatMap 用于处理可能为空的 stream
                        .filter(change -> Objects.nonNull(change) && CollUtil.isNotEmpty(change.getDetails()))
                        .filter(change -> change.getDetails().stream()
                                .anyMatch(d -> feeType.getCode().equals(d.getType()) && d.getEndDate() != null))
                        .findFirst() // 因为 historicalChangeRecords 已按ID倒序，所以这是最新的
                );

        final String associatedChangeRecordId = latestRelevantChangeOpt
                .map(PaymentChangeRecord::getId) // 获取这个最新相关变更单的ID
                .orElse(null);

        // 2. 从历史变更单中提取与当前 feeType 相关的第一个有效的"变更结束日期" (endDate)
        final Date feeTypeSpecificLatestChangeEndDate = Optional.ofNullable(historicalChangeRecords)
                .filter(CollUtil::isNotEmpty)
                .flatMap(changes -> changes.stream()
                        .filter(Objects::nonNull)
                        .filter(change -> CollUtil.isNotEmpty(change.getDetails()))
                        .flatMap(change -> change.getDetails().stream()
                                // 筛选出与当前 feeType 相关的 detail
                                .filter(detail -> feeType.getCode().equals(detail.getType()))
                                .map(PaymentChangeRecord.Detail::getEndDate) // 获取该 detail 的 endDate
                        )
                        .filter(Objects::nonNull) // 过滤掉为 null 的 endDate
                        .findFirst() // 获取流中的第一个（即来自最新相关变更单的第一个有效endDate）
                )
                .orElse(null);

        // 3. 计算 estimatedExpiryDate
        // 简化预估到期日的计算逻辑
        Date estimatedExpiryDate;
        if (feeTypeSpecificLatestChangeEndDate != null) {
            // 如果有变更结束日期，则预估到期日为变更结束日期+1天
            estimatedExpiryDate = DateUtil.offsetDay(feeTypeSpecificLatestChangeEndDate, 1);
        } else {
            // 否则预估到期日为合同开始日期
            estimatedExpiryDate = dto.getContractStartDate();
        }

        // 设置计算好的预估到期日到detail
        detailToUpdate.setEstimatedExpiryDate(estimatedExpiryDate);

        // 将关联的变更单ID保存
        detailToUpdate.setChangePaymentId(associatedChangeRecordId);


        // 5. 根据 associatedChangeRecordId 计算"（基于该变更的）已缴费金额"
        // 初始化为0，如果后续条件不满足，则保持为0
        BigDecimal paidAmountBasedOnThisChange = BigDecimal.ZERO;

        if (CollUtil.isNotEmpty(historicalPaymentRecords) && StrUtil.isNotBlank(associatedChangeRecordId)) {
            // 只有当存在历史缴费记录并且成功获取到关联的变更单ID时，才进行累加
            paidAmountBasedOnThisChange = historicalPaymentRecords.stream()
                    .flatMap(pr -> pr.getDetails().stream())
                    .filter(pd -> feeType.getCode().equals(pd.getType()) &&
                            associatedChangeRecordId.equals(pd.getChangePaymentId()) && // 关键：缴费项的changePaymentId必须匹配
                            pd.getPaymentAmount() != null) // 确保有缴费金额
                    .map(PaymentRecord.Detail::getPaymentAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        } else if (CollUtil.isNotEmpty(historicalPaymentRecords) && feeTypeSpecificLatestChangeEndDate == null) {
            // 如果没有关联变更单ID且变更结束日期为空（即预估到期日为合同开始日期），则累加所有该费用类型的已缴金额
            paidAmountBasedOnThisChange = historicalPaymentRecords.stream()
                    .flatMap(pr -> pr.getDetails().stream())
                    .filter(pd -> feeType.getCode().equals(pd.getType()) && pd.getPaymentAmount() != null)
                    .map(PaymentRecord.Detail::getPaymentAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        // 如果 associatedChangeRecordId 为空，或者 historicalPaymentRecords 为空，
        // paidAmountBasedOnThisChange 将保持其初始值 BigDecimal.ZERO。

        detailToUpdate.setPaidAmount(paidAmountBasedOnThisChange);
    }


    private void fetchUnpaidValueAddedBillsWithDetails(PaymentRecord.Detail detail, String elderlyId) {
        // 查询未缴费的增值服务账单
        Map<String, Object> params = new HashMap<>();
        params.put("elderlyId", elderlyId);
        params.put("isConfirm", "0"); // 未缴费
        List<UserValueAddedServiceBill> bills = userValueAddedServiceService.selectUserValueAddedServiceBill(params);

        if (CollUtil.isEmpty(bills)) {
            return;
        }

        // 计算总额
        BigDecimal totalFee = bills.stream()
                .map(UserValueAddedServiceBill::getFee)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 填充detail
        detail.setBillIds(bills.stream()
                .map(bill -> bill.getId().toString())
                .collect(Collectors.joining(","))); // 增值服务账单ids
        detail.setPaymentAmount(totalFee); // 缴费金额
    }

    /**
     * 查询用户余额
     *
     * @param userId
     * @return
     */
    @Override
    public BigDecimal fetchUserBalance(String userId) {
        BalanceInfo query = new BalanceInfo();
        query.setUserId(userId);
        List<BalanceInfo> balanceInfos = balanceInfoService.selectBalanceInfoList(query);
        if (balanceInfos.size() < 1) { // 创建账户
            BalanceInfo balanceInfo = new BalanceInfo();
            balanceInfo.setId(IdUtils.fastSimpleUUID());
            balanceInfo.setUserId(userId);
            balanceInfo.setLastAmount(BigDecimal.ZERO);
            balanceInfo.setAmount(BigDecimal.ZERO);
            balanceInfo.setCreateBy(String.valueOf(SecurityUtils.getUserId()));
            balanceInfo.setCreateTime(new Date());
            balanceInfoService.insertBalanceInfo(balanceInfo);
            return BigDecimal.ZERO;
        } else if (balanceInfos.size() > 1) {
            throw new ServiceException("该用户信息异常请联系管理员排查");
        }

        return balanceInfos.get(0).getAmount();
    }

    /**
     * 根据缴费记录ID查询缴费明细
     *
     * @param recordId 缴费记录ID
     * @return 缴费明细列表
     */
    @Override
    public List<PaymentDetailResp> selectPaymentDetailByRecordId(String recordId) {
        if (StrUtil.isBlank(recordId)) {
            throw new ServiceException("缴费记录ID不能为空");
        }

        // 查询缴费记录
        PaymentRecord paymentRecord = paymentRecordMapper.selectPaymentRecordById(recordId);
        if (paymentRecord == null) {
            throw new ServiceException("缴费记录不存在");
        }

        // 从缴费记录中获取缴费明细列表
        List<PaymentRecord.PaidDetail> paidDetails = paymentRecord.getPaidDetails();
        if (CollUtil.isEmpty(paidDetails)) {
            return new ArrayList<>();
        }

        // 转换为响应对象
        return paidDetails.stream().map(paidDetail -> {
            PaymentDetailResp detailResp = new PaymentDetailResp();
            detailResp.setPaidCost(paidDetail.getPaidCost());
            detailResp.setPaymentMethod(paidDetail.getPaymentMethod());
            return detailResp;
        }).collect(Collectors.toList());
    }

    /**
     * 查询保障金账户余额
     *
     * @param elderlyId 老人ID
     * @return 保障金余额
     */
    private BigDecimal fetchSecurityBalance(String elderlyId) {
        try {
            SecurityBalanceInfo query = new SecurityBalanceInfo();
            query.setElderlyId(elderlyId);
            List<SecurityBalanceInfo> securityBalanceInfos = securityBalanceInfoService.selectSecurityBalanceInfoList(query);

            if (securityBalanceInfos.isEmpty()) {
                return BigDecimal.ZERO; // 如果没有保障金账户，返回0
            } else if (securityBalanceInfos.size() == 1) {
                SecurityBalanceInfo info = securityBalanceInfos.get(0);
                return info.getAmount() != null ? info.getAmount() : BigDecimal.ZERO;
            } else {
                throw new ServiceException("该老人保障金账户信息异常，请联系管理员排查");
            }
        } catch (Exception e) {
            log.error("查询保障金账户余额失败，老人ID：{}", elderlyId, e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 获取收入分类统计
     *
     * @return 收入分类统计列表
     */
    @Override
    public List<Map<String, Object>> getIncomeClassification() {
        return paymentRecordMapper.getIncomeClassification();
    }

    /**
     * 获取各缴费方式累计缴费统计
     *
     * @return 各缴费方式累计缴费统计列表
     */
    @Override
    public List<Map<String, Object>> getPaymentMethodStatistics() {
        return paymentRecordMapper.getPaymentMethodStatistics();
    }

    /**
     * 查询缴费管理列表
     *
     * @param paymentRecord 查询条件
     * @return 缴费管理列表
     */
    @Override
    public List<PaymentManagementResp> selectPaymentManagementList(PaymentRecord paymentRecord) {
        return paymentRecordMapper.selectPaymentManagementList(paymentRecord);
    }
}

